import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/educational_resources/domain/usecases/get_ressources_pedagogiques_usecase.dart';
import 'package:kairos/features/educational_resources/domain/usecases/get_ressources_pedagogiques_filtres_usecase.dart';
import 'package:kairos/features/educational_resources/domain/usecases/get_ressource_pedagogique_attachment_usecase.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_state.dart';

class RessourcesPedagogiquesCubit extends Cubit<RessourcesPedagogiquesState> {
  final GetRessourcesPedagogiquesUseCase getRessourcesPedagogiquesUseCase;
  final GetRessourcesPedagogiquesFiltresUseCase getRessourcesPedagogiquesFiltresUseCase;
  final GetRessourcePedagogiqueAttachmentUseCase getRessourcePedagogiqueAttachmentUseCase;

  // Store the currently loaded resources to preserve them during attachment operations
  List<ResourcePedagogiqueEntity> _currentRessources = [];

  RessourcesPedagogiquesCubit({
    required this.getRessourcesPedagogiquesUseCase,
    required this.getRessourcesPedagogiquesFiltresUseCase,
    required this.getRessourcePedagogiqueAttachmentUseCase,
  }) : super(RessourcesPedagogiquesInitial());

  /// Fetches educational resources for a student.
  /// This method handles the state management for fetching resources,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(RessourcesPedagogiquesLoading());
    final failureOrRessources = await getRessourcesPedagogiquesUseCase(
      GetRessourcesPedagogiquesParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrRessources.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch educational resources: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch educational resources: Network Error'));
        } else {
          emit(RessourcesPedagogiquesError('Failed to fetch educational resources: Unknown Error'));
        }
      },
      (ressources) {
        _currentRessources = ressources; // Update the internal list
        emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.initial));
      },
    );
  }

  /// Fetches educational resources filtered by date range.
  /// This method handles the state management for fetching resources based on date range,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchRessourcesPedagogiquesByDateRange({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? dateDebut,
    String? dateFin,
  }) async {
    emit(RessourcesPedagogiquesLoading());
    final failureOrRessources = await getRessourcesPedagogiquesFiltresUseCase(
      GetRessourcesPedagogiquesFiltresParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        dateDebut: dateDebut,
        dateFin: dateFin,
      ),
    );

    failureOrRessources.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch filtered educational resources: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch filtered educational resources: Network Error'));
        } else {
          emit(RessourcesPedagogiquesError('Failed to fetch filtered educational resources: Unknown Error'));
        }
      },
      (ressources) {
        _currentRessources = ressources; // Update the internal list
        emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.initial));
      },
    );
  }

  /// Fetches attachment for a specific educational resource.
  /// This method preserves the current resources list while managing attachment state.
  Future<void> fetchRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idRessource,
    String? codeUtilisateur,
  }) async {
    // Update state to show attachment loading while preserving current resources
    if (state is RessourcesPedagogiquesLoaded) {
      emit((state as RessourcesPedagogiquesLoaded).copyWith(
        attachmentStatus: AttachmentStatus.loading,
      ));
    }

    final failureOrAttachment = await getRessourcePedagogiqueAttachmentUseCase(
      GetRessourcePedagogiqueAttachmentParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        idRessource: idRessource,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrAttachment.fold(
      (failure) {
        if (state is RessourcesPedagogiquesLoaded) {
          emit((state as RessourcesPedagogiquesLoaded).copyWith(
            attachmentStatus: AttachmentStatus.error,
            attachmentErrorMessage: 'Failed to fetch attachment',
          ));
        }
      },
      (attachment) {
        if (state is RessourcesPedagogiquesLoaded) {
          emit((state as RessourcesPedagogiquesLoaded).copyWith(
            attachmentStatus: AttachmentStatus.loaded,
          ));
        }
        // Handle the attachment (e.g., save to file, open with system app)
        // This logic would be similar to what's implemented in DossiersCubit
      },
    );
  }
}
