import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/profile_entity.dart';
import '../../domain/entities/carte_virtuelle_entity.dart';
import '../../domain/repositories/profile_repository.dart';
import '../datasources/profile_local_datasource.dart';
import '../datasources/profile_remote_datasource.dart';
import '../../../authentication/data/models/deconnexion_request.dart';
import '../../../authentication/data/models/delete_account_request.dart'; 
import '../models/carte_virtuelle_model.dart';

/// Implementation of ProfileRepository
class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileLocalDataSource localDataSource;
  final ProfileRemoteDataSource remoteDataSource;

  ProfileRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, ProfileEntity?>> getProfile(String codeUtilisateur) async {
    try {
      // Pass the codeUtilisateur to the local data source
      final userProfileModel = await localDataSource.getUserProfile(codeUtilisateur);
      if (userProfileModel != null) {
        // Convert UserProfileModel to ProfileEntity
        final profileEntity = ProfileEntity(
          id: userProfileModel.id,
          codeUtilisateur: userProfileModel.codeUtilisateur,
          profil: userProfileModel.profil,
          photo: userProfileModel.photo,
          isAuthenticated: userProfileModel.isAuthenticated,
          message: userProfileModel.message,
          fullName: userProfileModel.utilisateur.nomComplet,
          phoneNumber: localDataSource.getPhoneNumber() ?? "",
          schoolName: userProfileModel.etablissement.libelleEtab,
          schoolCode: userProfileModel.etablissement.codeEtab,
          schoolLogo: userProfileModel.etablissement.logoEtablissement,
        );
        return Right(profileEntity);
      }
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> logout(DeconnexionRequest request) async {
    try {
      await remoteDataSource.logout(request);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      return Left(ServerFailure('Failed to logout: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> removeProfile() async {
    try {
      await localDataSource.removeUserProfile();
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
  @override
  Future<Either<Failure, void>> deleteAccount(DeleteAccountRequest request) async {
    try {
      await remoteDataSource.deleteAccount(request);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      return Left(ServerFailure('Failed to delete account: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CarteVirtuelleEntity>> getCarteVirtuelle({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final result = await remoteDataSource.getCarteVirtuelle(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );
      return Right(result as CarteVirtuelleEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      return Left(ServerFailure('Failed to get virtual card: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
