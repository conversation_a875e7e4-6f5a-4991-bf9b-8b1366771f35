import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/emploi_temps_entity.dart';
import '../../domain/repositories/emplois_du_temps_repository.dart';
import '../datasources/emplois_du_temps_remote_datasource.dart';

/// Implementation of EmploisDuTempsRepository
class EmploisDuTempsRepositoryImpl implements EmploisDuTempsRepository {
  final EmploisDuTempsRemoteDataSource remoteDataSource;

  EmploisDuTempsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      debugPrint('EmploisDuTempsRepositoryImpl: Getting schedule data for student: $codeEtudiant');
      
      final scheduleData = await remoteDataSource.getEmploisDuTemps(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert models to entities
      final entities = scheduleData.map<List<EmploiTempsEntity>>((daySchedule) {
        return daySchedule.map<EmploiTempsEntity>((item) => item.toEntity()).toList();
      }).toList();

      debugPrint('EmploisDuTempsRepositoryImpl: Successfully retrieved ${entities.length} days of schedule data');
      return Right(entities);
    } on ServerException {
      debugPrint('EmploisDuTempsRepositoryImpl: Server exception occurred');
      return const Left(ServerFailure('Failed to get schedule data from server'));
    } on DioException catch (e) {
      debugPrint('EmploisDuTempsRepositoryImpl: Network error: ${e.message}');
      if (e.type == DioExceptionType.connectionTimeout || 
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        return const Left(NetworkFailure('Connection timeout'));
      } else if (e.type == DioExceptionType.connectionError) {
        return const Left(NetworkFailure('No internet connection'));
      } else {
        return Left(ServerFailure('Network error: ${e.message}'));
      }
    } catch (e) {
      debugPrint('EmploisDuTempsRepositoryImpl: Unexpected error: $e');
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    try {
      debugPrint('EmploisDuTempsRepositoryImpl: Getting filtered schedule data for student: $codeEtudiant, dates: $startDate to $endDate');
      
      final scheduleData = await remoteDataSource.getEmploisDuTempsFiltres(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        startDate: startDate,
        endDate: endDate,
      );

      // Convert models to entities
      final entities = scheduleData.map<List<EmploiTempsEntity>>((daySchedule) {
        return daySchedule.map<EmploiTempsEntity>((item) => item.toEntity()).toList();
      }).toList();

      debugPrint('EmploisDuTempsRepositoryImpl: Successfully retrieved ${entities.length} days of filtered schedule data');
      return Right(entities);
    } on ServerException {
      debugPrint('EmploisDuTempsRepositoryImpl: Server exception occurred during filtering');
      return const Left(ServerFailure('Failed to get filtered schedule data from server'));
    } on DioException catch (e) {
      debugPrint('EmploisDuTempsRepositoryImpl: Network error during filtering: ${e.message}');
      if (e.type == DioExceptionType.connectionTimeout || 
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        return const Left(NetworkFailure('Connection timeout'));
      } else if (e.type == DioExceptionType.connectionError) {
        return const Left(NetworkFailure('No internet connection'));
      } else {
        return Left(ServerFailure('Network error: ${e.message}'));
      }
    } catch (e) {
      debugPrint('EmploisDuTempsRepositoryImpl: Unexpected error during filtering: $e');
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> submitCourseContent({
    required String codeEtudiant,
    required String codeEtab,
    required String heureDebut,
    required String heureFin,
    required String dateCours,
    required int emploiDuTempsId,
    required int responsableId,
    required String observations,
    required String numeroTelephone,
    required String marqueTelephone,
    required String modelTelephone,
    required String imeiTelephone,
    required String numeroSerie,
  }) async {
    try {
      debugPrint('EmploisDuTempsRepositoryImpl: Submitting course content for emploiDuTempsId: $emploiDuTempsId');

      final result = await remoteDataSource.submitCourseContent(
        codeEtudiant: codeEtudiant,
        codeEtab: codeEtab,
        heureDebut: heureDebut,
        heureFin: heureFin,
        dateCours: dateCours,
        emploiDuTempsId: emploiDuTempsId,
        responsableId: responsableId,
        observations: observations,
        numeroTelephone: numeroTelephone,
        marqueTelephone: marqueTelephone,
        modelTelephone: modelTelephone,
        imeiTelephone: imeiTelephone,
        numeroSerie: numeroSerie,
      );

      debugPrint('EmploisDuTempsRepositoryImpl: Successfully submitted course content');
      return Right(result);
    } on ServerException {
      debugPrint('EmploisDuTempsRepositoryImpl: Server exception occurred during course content submission');
      return const Left(ServerFailure('Failed to submit course content to server'));
    } on DioException catch (e) {
      debugPrint('EmploisDuTempsRepositoryImpl: Network error during course content submission: ${e.message}');
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        return const Left(NetworkFailure('Connection timeout'));
      } else if (e.type == DioExceptionType.connectionError) {
        return const Left(NetworkFailure('No internet connection'));
      } else {
        return Left(ServerFailure('Network error: ${e.message}'));
      }
    } catch (e) {
      debugPrint('EmploisDuTempsRepositoryImpl: Unexpected error during course content submission: $e');
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }
}
