import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart'; // Correct import
import 'package:kairos/features/authentication/presentation/bloc/state/auth_state.dart'; // Correct import
import 'package:kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart'; // Correct import
import 'code_activation_error_dialog.widget.dart';
import 'package:flutter/gestures.dart';

class CodeActivationWidget extends StatefulWidget {
  const CodeActivationWidget({super.key, required this.pageController, required this.phoneNumber});
  final PageController pageController;
  final String phoneNumber;

  @override
  State<CodeActivationWidget> createState() => _CodeActivationWidgetState();
}

class _CodeActivationWidgetState extends State<CodeActivationWidget> {
  final TextEditingController _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener( // Use MultiBlocListener to listen to both Cubits
      listeners: [
        BlocListener<PhoneAuthenticationCubit, AuthState>( // Listener for PhoneAuthenticationCubit (for resend SMS)
          listener: (phoneAuthContext, state) {
            if (state is ResendingAuthSms) {
               setState(() {
                _isLoading = true;
              });
            } else if (state is ResendAuthSmsSuccess) {
              // Show success message for resend
              setState(() {
                _isLoading = false;
              });
              ScaffoldMessenger.of(phoneAuthContext).showSnackBar(
                CustomSnackbar(
                  message: "Code d'activation renvoyé au numéro: ${widget.phoneNumber}",
                ).getSnackBar(),
              );
            } else if (state is ResendAuthSmsError) {
               setState(() {
                _isLoading = false;
              });
               ScaffoldMessenger.of(phoneAuthContext).showSnackBar(
                CustomSnackbar(
                  message: "Erreur lors du renvoi du code: ${state.message}",
                  isError: true,
                ).getSnackBar(),
              );
            } // End of ResendAuthSmsError
          }, // End of PhoneAuthenticationCubit listener
        ), // End of PhoneAuthenticationCubit BlocListener
        BlocListener<CodeActivationCubit, AuthState>( // Listener for CodeActivationCubit (for verification errors from EnterFullnameWidget)
          listener: (context, state) { // Use context directly
            if (state is CodeActivationErrorState) { // Only handle error state here
              showDialog(
                context: context, // Use context directly
                builder: (BuildContext dialogContext) {
                  return CodeActivationErrorDialog(
                    returnCode: state.returnCode,
                    userMessage: state.userMessage,
                    onResend: () {
                      context.read<PhoneAuthenticationCubit>().resendSms(widget.phoneNumber); // Use context directly
                    },
                  );
                },
              );
            }
          },
        ),
      ],
      child: Column( // Main widget tree
        mainAxisAlignment: MainAxisAlignment.spaceEvenly, // Keep original alignment
        children: [ // Keep original children structure
          Text( // Correct Text widget usage
            "CODE D'ACTIVATION",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor, // Use context directly
            ),
          ),
          SvgPicture.asset("assets/images/logo_kairos.svg"), // Keep SvgPicture
          Divider( // Keep Divider
            color: Theme.of(context).primaryColor, // Use context directly
            thickness: 5,
            height: 20,
            indent: 100,
            endIndent: 100,
          ),
          const Spacer(), // Keep Spacer
          Flexible(flex: 8, child: SvgPicture.asset("assets/images/otp_code.svg")), // Keep Flexible and SvgPicture
          const Padding( // Keep Padding
            padding: EdgeInsets.symmetric(horizontal: 20.0), // Keep padding
            child: Text( // Correct Text widget usage
              "Veuillez saisir le code d'activation envoyé à votre numéro de téléphone.",
              textAlign: TextAlign.center,
            ),
          ),
          const Spacer(flex: 3), // Keep Spacer
          Form( // Keep Form
            key: _formKey, // Keep key
            child: SizedBox( // Keep SizedBox
              width: 300, // Keep width
              child: TextFormField( // Keep TextFormField
                controller: _codeController, // Keep controller
                decoration: InputDecoration( // Keep decoration
                  border: OutlineInputBorder(), // Keep border
                  hintText: "Code d'activation", // Keep hintText
                  hintStyle: TextStyle(color: Colors.grey.shade400), // Keep hintStyle
                  contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10), // Keep contentPadding
                ),
                keyboardType: TextInputType.number, // Keep keyboardType
                inputFormatters: [ // Keep inputFormatters
                  FilteringTextInputFormatter.digitsOnly, // Keep FilteringTextInputFormatter
                ],
                validator: (value) { // Keep validator
                  if (value!.isEmpty || value.length < 4) {
                    return "Veuillez saisir le code d'activation";
                  } else {
                    return null;
                  }
                },
              ),
            ),
          ),
          const Spacer(), // Keep Spacer
          FilledButton( // Keep FilledButton
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor), // Keep style
              fixedSize: WidgetStateProperty.all(Size(300, 50)),
            ),
            onPressed: _isLoading ? null : () async {
              debugPrint('the user clicked on `Continue` button');
              FocusScope.of(context).unfocus(); // Hide keyboard
              if (_formKey.currentState!.validate()) {
                // Update PhoneAuthenticationCubit state with the entered code and navigate
                FocusScope.of(context).unfocus();
                context.read<PhoneAuthenticationCubit>().setActivationCode(_codeController.text); // Update state
                widget.pageController.nextPage(duration: const Duration(milliseconds: 500), curve: Curves.easeInOut); // Navigate
              }
            },
            child: _isLoading // Keep loading indicator logic
                ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text( // Correct Text widget usage
                    "CONTINUER",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
          ),
          const Spacer(), // Keep Spacer
          Text.rich( // Correct Text.rich usage
            textAlign: TextAlign.center, // Keep textAlign
            TextSpan( // Correct TextSpan usage
              children: [ // Keep children
                const TextSpan(text: "Vous n'avez pas reçu le code d'activation? "), // Keep TextSpan
                TextSpan( // Keep TextSpan
                  text: "Renvoyer",
                  style: TextStyle(
                    color: Theme.of(context).primaryColor, // Use context directly
                    fontWeight: FontWeight.bold,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      context.read<PhoneAuthenticationCubit>().resendSms(widget.phoneNumber); // Use context directly
                    },
                ),
              ],
            ),
          ),
          const Spacer(flex: 3), // Keep Spacer
        ],
      ), // End of MultiBlocListener child
    ); // End of MultiBlocListener
  } // End of build method
} // End of _CodeActivationWidgetState class
