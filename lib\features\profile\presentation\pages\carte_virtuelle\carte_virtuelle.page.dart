import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart'; // Assuming qr_flutter is available
import 'package:flutter_bloc/flutter_bloc.dart'; // Import for BlocBuilder
import 'package:kairos/features/profile/presentation/bloc/profile_cubit.dart'; // Import for ProfileCubit
import 'package:kairos/features/profile/presentation/bloc/profile_state.dart'; // Import for ProfileState
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/profile/domain/entities/carte_virtuelle_entity.dart'; // Import for CarteVirtuelleEntity
import 'package:kairos/core/di/injection_container.dart' as di; // Import for sl
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart'; // Import for AuthLocalDataSource

class CarteVirtuellePage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant; // Made optional

  const CarteVirtuellePage({
    super.key,
    required this.school,
    this.etudiant, // Now optional
  });

  @override
  State<CarteVirtuellePage> createState() => _CarteVirtuellePageState();
}

class _CarteVirtuellePageState extends State<CarteVirtuellePage> {
    final AuthLocalDataSource _authLocalDataSource = di.sl<AuthLocalDataSource>();

  @override
  void initState() {
    super.initState();
    // Dispatch the event to fetch virtual card data when the page initializes
    _loadVirtualCard();
    
  }


  Future<void> _loadVirtualCard() async {
    try{
      final String? telephone = await _authLocalDataSource.getPhoneNumber();
      if(telephone != null){

        context.read<ProfileCubit>().getCarteVirtuelle(
          codeEtab: widget.school.codeEtab,
          telephone: telephone,
          codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur,
          codeUtilisateur: widget.etudiant != null? widget.school.codeUtilisateur: null,
        );
      }
    } catch(e){
      debugPrint("error loading the cartevirtuelle: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;

    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        CarteVirtuelleEntity? carteVirtuelle;
        bool isLoading = false;
        String? errorMessage;

        if (state is CarteVirtuelleLoading) {
          isLoading = true;
        } else if (state is CarteVirtuelleLoaded) {
          carteVirtuelle = state.carteVirtuelle;
        } else if (state is CarteVirtuelleError) {
          errorMessage = state.message;
        }

        if (isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (errorMessage != null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Ma carte virtuelle'),
              centerTitle: false,
              foregroundColor: Colors.white,
              backgroundColor: Colors.transparent,
              elevation: 0,
            ),
            body: Center(
              child: Text('Erreur de chargement de la carte virtuelle: $errorMessage'),
            ),
          );
        }

        if (carteVirtuelle == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Ma carte virtuelle'),
              centerTitle: false,
              foregroundColor: Colors.white,
              backgroundColor: Colors.transparent,
              elevation: 0,
            ),
            body: const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: Text('Impossible de charger les informations de la carte virtuelle.'),
              ),
            ),
          );
        }

        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            title: const Text('Ma carte virtuelle'),
            centerTitle: false,
            foregroundColor: Colors.white,
            backgroundColor: Colors.transparent,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: heroHeight + 150, // Extended height to accommodate floating card
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      // Hero background image
                      Hero(
                        tag: "hero_profile",
                        transitionOnUserGestures: true,
                        child: Image.asset(
                          "assets/images/header_dashboard.png",
                          width: MediaQuery.of(context).size.width,
                          fit: BoxFit.cover,
                        ),
                      ),
                      // Floating profile card positioned over hero section
                      Positioned(
                        top: 83, // 83px from screen top
                        left: (MediaQuery.of(context).size.width - 302) / 2, // Centered horizontally
                        child: _buildFloatingProfileCard(carteVirtuelle),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 0, left: 16, right: 16),
                  child: _buildQrCodeSection(carteVirtuelle),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFloatingProfileCard(CarteVirtuelleEntity carteVirtuelle) {
    return Stack(
      children: [
        Container(
          width: 302,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.25),
                offset: const Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              const SizedBox(height: 206),
              const SizedBox(height: 13),
              Text(
                '${carteVirtuelle.prenom} ${carteVirtuelle.nom}',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w900,
                  color: Colors.black,
                  height: 1.21,
                ),
              ),
              const SizedBox(height: 13),
              RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 10, color: Colors.black),
                  children: [
                    TextSpan(
                      text: 'Matricule: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    TextSpan(text: carteVirtuelle.codeUtilisateur),
                  ],
                ),
              ),
              RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 10, color: Colors.black),
                  children: [
                    TextSpan(
                      text: 'Programme: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    TextSpan(text: carteVirtuelle.programmeEnCours),
                  ],
                ),
              ),
              RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 10, color: Colors.black),
                  children: [
                    TextSpan(
                      text: 'Niveau: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    TextSpan(text: carteVirtuelle.niveauEnCours),
                  ],
                ),
              ),
              RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 10, color: Colors.black),
                  children: [
                    TextSpan(
                      text: 'Classe: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    TextSpan(text: carteVirtuelle.classeEnCours),
                  ],
                ),
              ),
              const SizedBox(height: 7),
            ],
          ),
        ),
        Positioned(
          left: 50.0,
          top: 10.0,
          child: _buildProfileImage(carteVirtuelle),
        ),
      ],
    );
  }

  Widget _buildProfileImage(CarteVirtuelleEntity carteVirtuelle) {
    ImageProvider backgroundImage;
    final String? photoData = carteVirtuelle.photo;

    if (photoData != null && photoData.isNotEmpty) {
      try {
        backgroundImage = MemoryImage(
          base64Decode(photoData.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
        );
      } catch (_) {
        backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
      }
    } else {
      backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
    }

    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 200,
        backgroundImage: backgroundImage,
      ),
    );
  }

  Widget _buildQrCodeSection(CarteVirtuelleEntity carteVirtuelle) {

    final String qrData = jsonEncode({
      'codeUtilisateur': carteVirtuelle.codeUtilisateur,
      'prenom': carteVirtuelle.prenom,
      'nom': carteVirtuelle.nom,
      'profil': carteVirtuelle.profil,
      'programmeEnCours': carteVirtuelle.programmeEnCours,
      'anneeScolaireEnCours': carteVirtuelle.anneeScolaireEnCours,
      'niveauEnCours': carteVirtuelle.niveauEnCours,
      'classeEnCours': carteVirtuelle.classeEnCours,
      'dateDerniereInscription': carteVirtuelle.dateDerniereInscription,
      'montantAPayer': carteVirtuelle.montantAPayer,
      'montantPaye': carteVirtuelle.montantPaye
    });

    return Center(
      child: Column(
        children: [
          const SizedBox(height: 100),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 4),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: QrImageView(
              data: qrData,
              version: QrVersions.auto,
              size: 200.0,
              gapless: false,
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

