import 'package:flutter/material.dart';
import 'package:kairos/features/course_log/domain/entities/course_log_entity.dart';
import 'course_log_item.widget.dart';

class CourseLogDayGroup extends StatelessWidget {
  final List<CourseLogEntity> dayLogs;

  const CourseLogDayGroup({
    super.key,
    required this.dayLogs,
  });

  @override
  Widget build(BuildContext context) {
    if (dayLogs.isEmpty) return const SizedBox.shrink();

    final date = dayLogs.first.dateCours;
    final weekdayAbbr = _getWeekdayAbbreviation(date);
    final dateStr = _formatDateForHeader(date);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 7.0, horizontal: 0.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Weekday and date column (left) - similar to schedule page
          Padding(
            padding: const EdgeInsets.only(right: 4.0, top: 4.0, left: 2.0),
            child: SizedBox(
              width: 60,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    weekdayAbbr,
                    style: const TextStyle(
                      fontSize: 21,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    dateStr,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Course log items column (right)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: dayLogs.map((courseLog) =>
                Padding(
                  padding: const EdgeInsets.only(bottom: 1.0),
                  child: CourseLogItem(courseLog: courseLog),
                )
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Extract weekday abbreviation from date string (e.g., "2024-04-17" -> "MAR")
  String _getWeekdayAbbreviation(String dateStr) {
    try {
      DateTime date = _parseDate(dateStr);
      
      // French weekday abbreviations matching Figma design
      const List<String> weekdayAbbr = [
        'LUN', 'MAR', 'MER', 'JEU', 'VEN', 'SAM', 'DIM'
      ];
      
      return weekdayAbbr[date.weekday - 1];
    } catch (e) {
      debugPrint("Error getting weekday abbreviation: $e");
      return '';
    }
  }

  /// Format date for header display (e.g., "2024-04-17" -> "17/04")
  String _formatDateForHeader(String dateStr) {
    try {
      DateTime date = _parseDate(dateStr);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}';
    } catch (e) {
      debugPrint("Error formatting date for header: $e");
      return dateStr;
    }
  }

  /// Parse date string in various formats
  DateTime _parseDate(String dateStr) {
    if (dateStr.contains('/')) {
      List<String> parts = dateStr.split('/');
      if (parts.length == 3) {
        // Format: dd/MM/yyyy
        return DateTime(int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
      } else if (parts.length == 2) {
        // Format: dd/MM - add current year
        int currentYear = DateTime.now().year;
        return DateTime(currentYear, int.parse(parts[1]), int.parse(parts[0]));
      }
    } else if (dateStr.contains('-')) {
      // Format: yyyy-MM-dd
      return DateTime.parse(dateStr);
    }
    throw FormatException("Unable to parse date: $dateStr");
  }
}
