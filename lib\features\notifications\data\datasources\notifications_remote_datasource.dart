import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/error/exceptions.dart';
import '../models/notifications_response_model.dart';

/// Remote data source for notifications
abstract class NotificationsRemoteDataSource {
  /// Get notifications from the API
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: Optional user code
  /// 
  /// Returns [NotificationsResponseModel] on success
  /// Throws [ServerException] on error
  Future<NotificationsResponseModel> getNotifications({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of NotificationsRemoteDataSource
class NotificationsRemoteDataSourceImpl implements NotificationsRemoteDataSource {
  final ApiClient apiClient;

  NotificationsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<NotificationsResponseModel> getNotifications({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final queryParameters = <String, dynamic>{
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+",''),
        'codeEtudiant': codeEtudiant,
        if (codeUtilisateur != null && codeUtilisateur.isNotEmpty)
          'codeUtilisateur': codeUtilisateur,
      };

      // Make API request
      final response = await apiClient.getWithToken(
        ApiEndpoints.notificationsEtudiant,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      final jsonReponse = latin1.decode(response.data);
      final decodedResponse = jsonDecode(jsonReponse);

      debugPrint('NotificationsRemoteDataSourceImpl: getNotifications raw response data: $decodedResponse');

      // Check response status
      if (response.statusCode == 200) {
        // Parse response data
        final responseData = decodedResponse;
        if (responseData != null) {
          return NotificationsResponseModel.fromJson(responseData);
        } else {
          throw ServerException('Empty response data');
        }
      } else {
        throw ServerException(
          'Failed to get notifications: ${response.statusCode} - ${response.statusMessage}',
        );
      }
    } on DioException catch (e) {
      // Handle Dio-specific errors
      if (e.response != null) {
        throw ServerException(
          'API Error: ${e.response?.statusCode} - ${e.response?.statusMessage}',
        );
      } else {
        throw ServerException('Network Error: ${e.message}');
      }
    } catch (e) {
      // Handle any other errors
      throw ServerException('Unexpected error: ${e.toString()}');
    }
  }
}
