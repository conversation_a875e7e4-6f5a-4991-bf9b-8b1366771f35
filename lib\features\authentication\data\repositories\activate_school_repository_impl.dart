import 'dart:async';

import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/data/datasources/activate_school_remote_datasource.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:Kairos/features/schools/data/models/user_profile_model.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

import '../../domain/repositories/activate_school_repository.dart';

/// Concrete implementation of [ActivateSchoolRepository].
///
/// Provides the actual implementation for school activation operations.
class ActivateSchoolRepositoryImpl implements ActivateSchoolRepository {
  /// Constructs an [ActivateSchoolRepositoryImpl].
  ///
  /// Dependencies like data sources would be injected here in a real scenario.
  ActivateSchoolRepositoryImpl({required this.remoteDataSource, required this.authLocalDataSource});

  final ActivateSchoolRemoteDatasource remoteDataSource;
  final AuthLocalDataSource authLocalDataSource;

  @override
  Future<Either<Failure, List<UserProfileModel>>> activateSchool(ActivationRequestEntity request) async {
    try {
      final String? numeroTelephone = await authLocalDataSource.getPhoneNumber();
      debugPrint("ActivateSchoolRepositoryImpl: numeroTelephone: $numeroTelephone");
      final userProfiles = await remoteDataSource.activateSchool(request.toModel(DeviceInfoService.deviceInfo, numeroTelephone!));
      return Right(userProfiles); // Indicate success
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message, returnCode: e.returnCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
       // Handle Dio errors (HTTP errors, network issues, etc.)
       debugPrint("ActivateSchoolRepositoryImpl: DioException: $e");
       debugPrint("ActivateSchoolRepositoryImpl: DioException response: ${e.message}");
      return Left(ServerFailure('Failed to activate school: ${e.message}', returnCode: e.response?.data['returnCode']));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}