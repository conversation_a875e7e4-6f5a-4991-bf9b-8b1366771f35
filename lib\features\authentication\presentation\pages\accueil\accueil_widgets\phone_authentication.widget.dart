import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';
import '../../../bloc/state/auth_state.dart';
import '../../../bloc/cubit/phone_authentication_cubit.dart';
import 'reactivate_dialog.widget.dart';

class PhoneAuthentication extends StatelessWidget {
  const PhoneAuthentication({super.key, required this.pageController});
  final PageController pageController;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PhoneAuthenticationCubit, AuthState>(
      listener: (phoneAuthenticationContext, state) {
        if (state is AuthSmsSent) {
          // Show success message
          ScaffoldMessenger.of(phoneAuthenticationContext).showSnackBar(
            CustomSnackbar(
              message: "Code d'activation envoyé au numéro: ${state.phoneNumber}",
            ).getSnackBar(),
          );
          // Navigate to next pagep
          pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else if (state is AuthSmsError) {
          debugPrint("PhoneActivationWidget: AuthSmsError ------>: ${state.message}");
          showDialog(
            context: phoneAuthenticationContext,
            builder: (BuildContext context) {
              return ReactivateDialog(
                onReactivate: () {
                  if (state.phoneNumber != null) {
                    // Call checkResponse with activated: true for OUI
                    phoneAuthenticationContext.read<PhoneAuthenticationCubit>().checkResponse(state.phoneNumber!, activated: true);
                  }
                },
                onCancel: () {
                   if (state.phoneNumber != null) {
                    // Call checkResponse with activated: false for NON
                    phoneAuthenticationContext.read<PhoneAuthenticationCubit>().checkResponse(state.phoneNumber!, activated: false);
                  }
                },
              );
            },
          );
        }
      },
      builder: (context, state) {
        return _PhoneAuthenticationForm(
          pageController: pageController,
          isLoading: state is AuthSmsSending,
        );
      },
    );
  }
}

class _PhoneAuthenticationForm extends StatefulWidget {
  const _PhoneAuthenticationForm({
    required this.pageController,
    required this.isLoading,
  });

  final PageController pageController;
  final bool isLoading;

  @override
  State<_PhoneAuthenticationForm> createState() => _PhoneAuthenticationFormState();
}

class _PhoneAuthenticationFormState extends State<_PhoneAuthenticationForm> {
  final TextEditingController _textEditingController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  PhoneNumber? _phoneNumber;

  @override
  void dispose() {
    _textEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text(
          "AUTHENTIFICATION",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        SvgPicture.asset("assets/images/logo_kairos.svg"),
        Divider(
          color: Theme.of(context).primaryColor,
          thickness: 5,
          height: 20,
          indent: 100,
          endIndent: 100,
        ),
        const Spacer(),
        Flexible(
          flex: 8,
          child: SvgPicture.asset("assets/images/phone_auth.svg"),
        ),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.0),
          child: Text(
            "Veuillez saisir votre numéro de téléphone pour recevoir votre code d'activation.",
            textAlign: TextAlign.center,
          ),
        ),
        const Spacer(),
        Form(
          key: _formKey,
          child: SizedBox(
            width: 300,
            child: IntlPhoneField(
              controller: _textEditingController,
              languageCode: 'fr',
              decoration: InputDecoration(
                border: const OutlineInputBorder(),
                hintText: "77 123 45 67",
                hintStyle: TextStyle(color: Colors.grey.shade400),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 10,
                ),
              ),
              initialCountryCode: 'SN',
              flagsButtonPadding: const EdgeInsets.only(left: 10),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              pickerDialogStyle: PickerDialogStyle(
                searchFieldInputDecoration: InputDecoration(
                  hintText: "Rechercher un pays",
                  hintStyle: TextStyle(color: Colors.grey.shade400),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(1.0),
                  ),
                  suffixIcon: const Icon(Icons.search),
                ),
              ),
              dropdownIconPosition: IconPosition.trailing,
              dropdownTextStyle: const TextStyle(color: Colors.black),
              validator: (value) {
                debugPrint("Phone number: $value");
                if (value?.isValidNumber() == false) {
                  return "Veuillez saisir un numéro de téléphone valide";
                } else if (value?.completeNumber.isEmpty == true) {
                  return "Veuillez saisir votre numéro de téléphone";
                } else {
                  return null;
                }
              },
              onChanged: (PhoneNumber phoneNumber) {
                _phoneNumber = phoneNumber;
                debugPrint("Phone number changed: $phoneNumber");
              },
            ),
          ),
        ),
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            fixedSize: WidgetStateProperty.all(const Size(300, 50)),
          ),
          onPressed: widget.isLoading ? null : () {
            debugPrint('User clicked on Continue button');
            if (_formKey.currentState!.validate()) {
              if (_phoneNumber != null) {
                // Trigger SMS sending via BLoC
                context.read<PhoneAuthenticationCubit>().sendSms(
                  _phoneNumber!.completeNumber,
                );
              }
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                CustomSnackbar(
                  message: "Veuillez saisir un numéro de téléphone valide",
                ).getSnackBar(),
              );
            }
          },
          child: widget.isLoading
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Text("SUIVANT"),
        ),
        const Spacer(flex: 2),
      ],
    );
  }
}