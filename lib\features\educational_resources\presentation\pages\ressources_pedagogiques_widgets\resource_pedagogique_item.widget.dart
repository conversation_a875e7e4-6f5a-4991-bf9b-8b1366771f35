import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_cubit.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/educational_resources/presentation/pages/ressources_pedagogiques_widgets/resource_pedagogique_details_dialog.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class ResourcePedagogiqueItem extends StatelessWidget {
  final ResourcePedagogiqueEntity ressource;
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const ResourcePedagogiqueItem({
    super.key,
    required this.ressource,
    this.school,
    this.etudiant,
  });



  String _formatDate(String dateString) {
    try {
      // Assuming the date comes in a parseable format
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      // Return the original string if parsing fails
      return dateString;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(5),
        shadowColor: Colors.black.withValues(alpha: 0.25),
        child: InkWell(
          borderRadius: BorderRadius.circular(5),
          onTap: () {
            showDialog(
              context: context,
              builder: (dialogContext) => BlocProvider.value(
                value: context.read<RessourcesPedagogiquesCubit>(),
                child: ResourcePedagogiqueDetailsDialog(
                  ressource: ressource,
                  school: school,
                  etudiant: etudiant,
                ),
              ),
            );
          },
          child: Container(
            height: 55,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(5),
              border: Border.all(
                color: Colors.black,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Resource indicator circle
                Container(
                  margin: const EdgeInsets.only(left: 6, right: 15),
                  width: 42,
                  height: 41,
                  decoration: BoxDecoration(
                    color: const Color(0xFF8E0101), // Default color for educational resources
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      'R', // R for Resource
                      style: const TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                // Content section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Resource type and date row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Ressource pédagogique',
                            style: TextStyle(

                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                              color: const Color(0xFFB16D60).withValues(alpha: 0.8),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(right: 5),
                            child: Row(
                              children: [
                                if (ressource.documents.isNotEmpty)
                                Icon(
                                Icons.attach_file,
                                size: 13,
                                color: Colors.grey[600],
                                ),
                                Text(
                                  _formatDate(ressource.dateAjout),
                                  style: TextStyle(

                                    fontWeight: FontWeight.w400,
                                    fontSize: 10,
                                    color: Colors.black.withValues(alpha: 0.5),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      // Description
                      Padding(
                        padding: const EdgeInsets.only(right: 90),
                        child: Text(
                          ressource.description,
                          style: const TextStyle(

                            fontWeight: FontWeight.w700,
                            fontSize: 12,
                            color: Colors.black,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Professor and class info
                      Padding(
                        padding: const EdgeInsets.only(right: 90),
                        child: Text(
                          'Avec ${ressource.professeur} en ${ressource.classe}',
                          style: TextStyle(
                           
                            fontWeight: FontWeight.w400,
                            fontSize: 9,
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                // Attachment icon
                
              ],
            ),
          ),
        ),
      ),
    );
  }
}
