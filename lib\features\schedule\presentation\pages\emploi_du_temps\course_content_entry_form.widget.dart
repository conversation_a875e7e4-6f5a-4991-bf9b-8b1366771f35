import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/course_content_entry_cubit.dart';
import '../../bloc/course_content_entry_state.dart';
import '../../../../../core/widgets/common/snackbar_widget.dart';
import '../../../../../features/authentication/data/datasources/auth_local_datasource.dart';
import '../../../../../core/di/injection_container.dart';
import 'package:Kairos/features/schedule/domain/entities/emploi_temps_entity.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';


class CourseContentEntryForm extends StatefulWidget {
  final EmploiTempsEntity scheduleItem;
  final Function(String courseName, String teacherName, String timeRange) onSuccess;
  final VoidCallback onCancel;
  final String codeEtab;
  final String codeEtudiant;
  final EtablissementUtilisateur currentUser;

  const CourseContentEntryForm({
    super.key,
    required this.scheduleItem,
    required this.onSuccess,
    required this.currentUser,
    required this.onCancel,
    required this.codeEtab,
    required this.codeEtudiant,
  });

  @override
  State<CourseContentEntryForm> createState() => _CourseContentEntryFormState();
}

class _CourseContentEntryFormState extends State<CourseContentEntryForm> {
  late TextEditingController _heureDebutController;
  late TextEditingController _heureFinController;
  late TextEditingController _observationsController;
  late AuthLocalDataSource _authLocalDataSource;

  @override
  void initState() {
    super.initState();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    
    // Initialize controllers with schedule item data
    final heureParts = widget.scheduleItem.heure.split(' - ');
    _heureDebutController = TextEditingController(
      text: heureParts.isNotEmpty ? heureParts[0] : '',
    );
    _heureFinController = TextEditingController(
      text: heureParts.length > 1 ? heureParts[1] : '',
    );
    _observationsController = TextEditingController();
  }

  @override
  void dispose() {
    _heureDebutController.dispose();
    _heureFinController.dispose();
    _observationsController.dispose();
    super.dispose();
  }

  Future<void> _submitForm() async {
    if (_observationsController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          message: "Veuillez saisir les observations",
          isError: true,
        ).getSnackBar(),
      );
      return;
    }

    try {
      // Get device info and user data
      final deviceInfo = await _authLocalDataSource.getCachedDeviceInfo();
      final phoneNumber = await _authLocalDataSource.getPhoneNumber() ?? '';
      
      // Submit the form
      if (context.mounted) {
        context.read<CourseContentEntryCubit>().submitCourseContent(
          codeEtudiant: widget.codeEtudiant,
          codeEtab: widget.codeEtab,
          heureDebut: _heureDebutController.text,
          heureFin: _heureFinController.text,
          dateCours: widget.scheduleItem.date,
          emploiDuTempsId: widget.scheduleItem.emploiDuTempsId ?? 0,
          responsableId: widget.scheduleItem.responsableId ?? 0,
          observations: _observationsController.text,
          numeroTelephone: phoneNumber,
          marqueTelephone: deviceInfo?['marqueTelephone'] ?? '',
          modelTelephone: deviceInfo?['modelTelephone'] ?? '',
          imeiTelephone: deviceInfo?['imeiTelephone'] ?? '',
          numeroSerie: deviceInfo?['numeroSerie'] ?? '',
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          CustomSnackbar(
            message: "Erreur lors de la soumission: $e",
            isError: true,
          ).getSnackBar(),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CourseContentEntryCubit, CourseContentEntryState>(
      listener: (context, state) {
        if (state is CourseContentEntrySuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Saisie enregistrée avec succès",
            ).getSnackBar(),
          );
          widget.onSuccess(
            widget.scheduleItem.cours,
            widget.scheduleItem.professeur,
            widget.scheduleItem.heure,
          );
        } else if (state is CourseContentEntryError) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Erreur lors de la saisie: ${state.message}",
              isError: true,
            ).getSnackBar(),
          );
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Builder(
          builder: (context){
            return CustomScrollView(
              slivers: [
                CustomAppBar(
                  pageSection: HeaderEnum.cahierDeTexte,
                  title: "SAISIE CAHIER DE TEXTE",
                  isSearchBarVisible: false,
                  etablissementUtilisateur: widget.currentUser,
                  isActionButtonVisible: false
                ),
                SliverFillRemaining(
                  child:Padding(
                    padding: const EdgeInsets.symmetric( vertical: 8.0, horizontal: 20),
                    child: SingleChildScrollView(
                      child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        // Course info header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            spacing: 10,
                            children: [
                              Icon(
                                Icons.badge,
                                size: 24,
                                color: Colors.black,
                              ),
                              Text(
                                "${widget.scheduleItem.cours} / ${widget.scheduleItem.classe} / ${widget.scheduleItem.semestre}",
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text.rich(
                                TextSpan(children: [
                                  const TextSpan(
                                    text: "Cours : ",
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: widget.scheduleItem.cours,
                                    style: const TextStyle(
                                      fontSize: 10,
                                    ),
                                  ),
                                ]
                              ),
                              ),
                              const SizedBox(height: 4),
                              Text.rich(
                                TextSpan(children: [
                                  const TextSpan(
                                    text: "Enseignant : ",
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: widget.scheduleItem.professeur,
                                    style: const TextStyle(
                                      fontSize: 10,
                                    ),
                                  ),
                                ]
                              ),
                              ),
                          const SizedBox(height: 8),
                        // Time fields
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Heure début *",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 60,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey[300]!),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Text(
                                      _heureDebutController.text,
                                      style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            const Icon(Icons.access_time_filled_outlined, size: 32),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Heure fin *",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    height: 60,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey[300]!),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Text(
                                      _heureFinController.text,
                                      style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // Observations field
                        const Text(
                          "Observations *",
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _observationsController,
                          maxLines: 6,
                          style: const TextStyle(
                            fontSize: 10
                          ),
                          decoration: InputDecoration(
                            hintText: "Saisissez vos observations",
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: const BorderSide(color: Color(0xFF953324)),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        // Submit button
                        BlocBuilder<CourseContentEntryCubit, CourseContentEntryState>(
                          builder: (context, state) {
                            final isLoading = state is CourseContentEntryLoading;
                            
                            return SizedBox(
                              width: double.infinity,
                              height: 48,
                              child: ElevatedButton(
                                onPressed: isLoading ? null : _submitForm,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF00BCD4),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                ),
                                child: isLoading
                                    ? const CircularProgressIndicator(color: Colors.white)
                                    : const Text(
                                        "CONFIRMER",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                              ),
                            );
                          },
                        ),
                            ]
                          )
                          )
                          
                          ],
                                      ),
                    ),
                  ),
              ),
              ]
            );
          }
        ),
      ),
    );
  }
}
