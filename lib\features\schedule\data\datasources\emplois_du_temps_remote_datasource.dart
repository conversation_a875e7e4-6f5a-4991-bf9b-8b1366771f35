import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/emploi_temps_item.dart';

/// Abstract interface for schedule remote data source
abstract class EmploisDuTempsRemoteDataSource {
  /// Get schedule/timetable data from API
  Future<List<List<EmploiTempsItem>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered schedule/timetable data from API
  Future<List<List<EmploiTempsItem>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  });

  /// Submit course content entry to API
  Future<String> submitCourseContent({
    required String codeEtudiant,
    required String codeEtab,
    required String heureDebut,
    required String heureFin,
    required String dateCours,
    required int emploiDuTempsId,
    required int responsableId,
    required String observations,
    required String numeroTelephone,
    required String marqueTelephone,
    required String modelTelephone,
    required String imeiTelephone,
    required String numeroSerie,
  });
}

/// Implementation of EmploisDuTempsRemoteDataSource
class EmploisDuTempsRemoteDataSourceImpl implements EmploisDuTempsRemoteDataSource {
  final ApiClient apiClient;

  EmploisDuTempsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<List<EmploiTempsItem>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      debugPrint('EmploisDuTempsRemoteDataSource: Calling getEmploisDuTemps with params: $queryParameters');

      // Make HTTP GET request to the emploisDuTemps endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.emploisDuTemps,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      debugPrint('EmploisDuTempsRemoteDataSource: Received response with status code: ${response.statusCode}');

      final String responseJsonString = latin1.decode(response.data);
      debugPrint('EmploisDuTempsRemoteDataSource: Decoded response string: $responseJsonString');

      final decodedResponse = jsonDecode(responseJsonString);
      debugPrint('EmploisDuTempsRemoteDataSource: Decoded JSON object: $decodedResponse');

      // Parse response as map of date -> list of items
      if (decodedResponse is Map<String, dynamic>) {
        final entries = decodedResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        final List<List<EmploiTempsItem>> scheduleData = entries.map<List<EmploiTempsItem>>((entry) {
          final daySchedule = entry.value;
          if (daySchedule is List) {
            return daySchedule.map<EmploiTempsItem>((item) => EmploiTempsItem.fromJson(item)).toList();
          }

          return <EmploiTempsItem>[];
        }).toList();
        debugPrint('EmploisDuTempsRemoteDataSource: Parsed schedule data: $scheduleData');
        return scheduleData;
      }

      return [];
    } catch (e) {
      debugPrint('EmploisDuTempsRemoteDataSource: Error getting schedule data: $e');
      throw Exception('Failed to get schedule data: $e');
    }
  }

  @override
  Future<List<List<EmploiTempsItem>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      final Map<String, dynamic> queryParameters = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur for PAR profile if provided
      if (codeUtilisateur != null && codeUtilisateur.isNotEmpty) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Add date filters if provided
      if (startDate != null && startDate.isNotEmpty) {
        queryParameters['dateDebut'] = startDate;
      }
      if (endDate != null && endDate.isNotEmpty) {
        queryParameters['dateFin'] = endDate;
      }

      debugPrint('EmploisDuTempsRemoteDataSource: Calling getEmploisDuTempsFiltres with params: $queryParameters');

      // Make HTTP GET request to the emploisDuTempsFiltres endpoint
      final response = await apiClient.getWithToken(
        ApiEndpoints.emploisDuTempsFiltres,
        queryParameters: queryParameters,
        options: Options(responseType: ResponseType.bytes)
      );

      debugPrint('EmploisDuTempsRemoteDataSource: Received filtered response with status code: ${response.statusCode}');

      final String responseJsonString = latin1.decode(response.data);
      final decodedResponse = jsonDecode(responseJsonString);

      // Parse response as map of date -> list of items
      if (decodedResponse is Map<String, dynamic>) {
        final entries = decodedResponse.entries.toList()
          ..sort((a, b) => a.key.compareTo(b.key)); // sort by date ascending
        return entries.map<List<EmploiTempsItem>>((entry) {
          final daySchedule = entry.value;
          if (daySchedule is List) {
            return daySchedule.map<EmploiTempsItem>((item) => EmploiTempsItem.fromJson(item)).toList();
          }
          return <EmploiTempsItem>[];
        }).toList();
      }

      return [];
    } catch (e) {
      debugPrint('EmploisDuTempsRemoteDataSource: Error getting filtered schedule data: $e');
      throw Exception('Failed to get filtered schedule data: $e');
    }
  }

  @override
  Future<String> submitCourseContent({
    required String codeEtudiant,
    required String codeEtab,
    required String heureDebut,
    required String heureFin,
    required String dateCours,
    required int emploiDuTempsId,
    required int responsableId,
    required String observations,
    required String numeroTelephone,
    required String marqueTelephone,
    required String modelTelephone,
    required String imeiTelephone,
    required String numeroSerie,
  }) async {
    try {
      // Build request payload
      final Map<String, dynamic> payload = {
        'codeEtudiant': codeEtudiant,
        'codeEtab': codeEtab,
        'heureDebut': heureDebut,
        'heureFin': heureFin,
        'dateCours': dateCours,
        'emploiDuTempsId': emploiDuTempsId,
        'responsableId': responsableId,
        'observations': observations,
        'numeroTelephone': numeroTelephone,
        'marqueTelephone': marqueTelephone,
        'modelTelephone': modelTelephone,
        'imeiTelephone': imeiTelephone,
        'numeroSerie': numeroSerie,
      };

      debugPrint('EmploisDuTempsRemoteDataSource: Submitting course content with payload: $payload');

      // Make HTTP POST request to the saisiCahierTexte endpoint
      final response = await apiClient.postWithToken(
        ApiEndpoints.saisiCahierTexte,
        data: payload,
      );

      debugPrint('EmploisDuTempsRemoteDataSource: Received course content submission response with status code: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return 'Course content submitted successfully';
      } else {
        throw Exception('Failed to submit course content: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('EmploisDuTempsRemoteDataSource: Error submitting course content: $e');
      throw Exception('Failed to submit course content: $e');
    }
  }
}
