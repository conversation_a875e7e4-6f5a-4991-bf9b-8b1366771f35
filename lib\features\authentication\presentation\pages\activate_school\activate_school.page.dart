import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/core/widgets/dialogs/custom_error_dialog.widget.dart';
import 'package:kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:kairos/features/authentication/presentation/bloc/state/activate_school_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_entity.dart';
import 'package:kairos/features/schools/presentation/pages/widgets/school_selection_modal.widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/di/injection_container.dart'; // Import sl<T> for dependency injection
import 'package:kairos/features/authentication/presentation/bloc/cubit/activate_school_cubit.dart';
import 'package:kairos/features/schools/presentation/bloc/schools_cubit.dart'; // Keep SchoolsCubit import for the modal

class ActivateSchoolPage extends StatefulWidget {
  const ActivateSchoolPage({super.key});

  @override
  State<ActivateSchoolPage> createState() => _ActivateSchoolPageState();
}

class _ActivateSchoolPageState extends State<ActivateSchoolPage> {
  // State variable to hold the selected school
  EtablissementEntity? _selectedSchool;

  // Text editing controllers for input fields
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  // Add a GlobalKey for the form
  final _formKey = GlobalKey<FormState>();
  @override
  void dispose() {
    // Dispose controllers when the widget is removed
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }


// ===================================== _showSchoolSelectionModal =================================
   // Method to show the school selection bottom modal
  void _showSchoolSelectionModal() async {
    // Load schools data before showing the modal
    // Note: This still uses SchoolsCubit to get the list of schools for the modal
    context.read<SchoolsCubit>().getSchools();

    final EtablissementEntity? selectedSchool = await showModalBottomSheet<EtablissementEntity?>(
      context: context,
      builder: (BuildContext context) {
        // Use the SchoolSelectionModal widget
        return SchoolSelectionModal();
      },
    );

    if (selectedSchool != null) {
      setState(() {
        _selectedSchool = selectedSchool;
      });
    }
  }

// ===================================== _showAuthenticationErrorDialog =================================
  /// Show custom error dialog for authentication errors
  void _showAuthenticationErrorDialog(BuildContext context, String message) {
    CustomErrorDialog.show(
      context,
      message: message,
      title: 'Erreur d\'authentification',
      iconPath: 'assets/icons/icone_acces_refuse.svg',
      iconColor: Colors.red,
      barrierDismissible: false,
    );
  }

// ===================================== build =================================
  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActivateSchoolCubit>(
      create: (context) => sl<ActivateSchoolCubit>(), // Provide ActivateSchoolCubit using GetIt
      child: BlocConsumer<ActivateSchoolCubit, ActivateSchoolState>(
        listener: (context, state) {
          if (state is ActivateSchoolSuccess) {
            // Show success snackbar
            CustomSnackbar(message: 'Authentification réussie !').getSnackBar();

            // Navigate to the list of establishments page on success
            Navigator.pop(context, state.userProfiles);
          } else if (state is ActivateSchoolError) {
            // Check if this is an authentication error (not authorized)
            if (state.errorMessage.contains('introuvable') || state.errorMessage.contains('n\'exite pas!')) {
              // Show alert dialog for authentication errors
              final String message = "Les identifiants fournis ne correspondent à aucun profil. Veuillez vérifier vos informations.";
              _showAuthenticationErrorDialog(context, message);
            } else {
              _showAuthenticationErrorDialog(context, state.errorMessage);
            }
          }
        },
        builder: (context, state) {
          // Determine loading state from Cubit state
          final bool isLoading = state is ActivateSchoolLoading;

          return Scaffold(
            extendBodyBehindAppBar: true,
            body: CustomScrollView(
              slivers: [
                CustomAppBar(
                  pageSection: HeaderEnum.dashboard, // Using dashboard as a placeholder
                  title: "", // Empty title as requested
                  isSearchBarVisible: false,
                  isActionButtonVisible: false,
                ),
                SliverToBoxAdapter( // Wrap the rest of the body in SliverToBoxAdapter
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(height: 20),
                      Text("ACTIVATION D'UN NOUVEL ÉTABLISSEMENT", textAlign: TextAlign.center, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                      Divider(color: Theme.of(context).primaryColor, thickness: 4, height: 20, indent: 150, endIndent: 150,),
                      Text(
                        "Nouvel établissement",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      SizedBox(height: 2),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          "Pour activer un nouvel établissement, veuillez choisir l'établissement, votre identifiant ainsi que votre mot de passe",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                      SizedBox(height: 10), // Adjusted spacing below AppBar
Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "ETABLISSEMENT",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.lightBlueAccent,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              InkWell(
                                onTap: _showSchoolSelectionModal,
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        _selectedSchool?.libelleEtab ?? "Ecole",
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: _selectedSchool == null ? Colors.grey.shade600 : Colors.black,
                                        ),
                                      ),
                                      Icon(Icons.arrow_drop_down, color: Colors.black),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "VOTRE IDENTIFIANT",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.lightBlueAccent,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              TextFormField(
                                controller: _usernameController,
                                decoration: InputDecoration(
                                  hintText: "222999",
                                  hintStyle: TextStyle(color: Colors.grey.shade400),
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return "Veuillez saisir votre identifiant";
                                  } else {
                                    return null;
                                  }
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "VOTRE MOT DE PASSE",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.lightBlueAccent,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              TextFormField(
                                controller: _passwordController,
                                decoration: InputDecoration(
                                  hintText: "********",
                                  hintStyle: TextStyle(color: Colors.grey.shade400),
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                ),
                                obscureText: true,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return "Veuillez saisir votre mot de passe";
                                  } else {
                                    return null;
                                  }
                                },
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      debugPrint("mot de passe oublié button -->");
                                      Navigator.pushNamed(context, "/password_reset");
                                    },
                                    child: Text(
                                      "Mot de passe oublié?",
                                      style: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                        decoration: TextDecoration.underline,
                                        decorationColor: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                          SizedBox(height: 30),
                          FilledButton(
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                              minimumSize: WidgetStateProperty.all(Size(150, 60)),
                            ),
                            onPressed: isLoading ? null : () {
                              // Dismiss keyboard
                              FocusScope.of(context).unfocus();
                              if (_selectedSchool == null) {
                                ScaffoldMessenger.of(context).showSnackBar(CustomSnackbar(message: "Veuillez sélectionner un établissement.").getSnackBar())
                                ;
                                return;
                              } else if(_formKey.currentState!.validate()) {
                                final String username = _usernameController.text;
                                final String password = _passwordController.text;
                                final String codeEtab = _selectedSchool!.codeEtab;
                                final ActivationRequestEntity request = ActivationRequestEntity(
                                  codeEtab: codeEtab,
                                  username: username,
                                  password: password,
                                );
                                context.read<ActivateSchoolCubit>().activateSchool(request);
                              }
                            },
                            child: state is ActivateSchoolLoading?
                                const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    "ACTIVER",
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                          ),
                          SizedBox(height: 60),
                          SvgPicture.asset(
                            'assets/images/logo_footer.svg',
                            height: 30,
                          ),
                          SizedBox(height: 10),
                        ],
                      ),
                    ),
                  ),
                  ]
                    ),
                ),
            ],
          ),
        );
      }
      
      ),
      
      
      );
     // Close BlocProvider
  }

 
}
