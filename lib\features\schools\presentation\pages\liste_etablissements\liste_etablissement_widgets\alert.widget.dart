import 'package:kairos/core/theme/color_schemes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/core/di/injection_container.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/dashboard/presentation/pages/dashboard/dashboard.page.dart';
import 'package:kairos/features/dashboard/presentation/bloc/dashboard_cubit.dart';

class AlertWidget extends StatelessWidget {
  final String message;
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? student;

  const AlertWidget({
    super.key,
    required this.message,
    required this.school,
    this.student,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: Stack(
        children: [
          Center(
            child: Text(
              "ACCÉS REFUSÉ",
              textAlign: TextAlign.center,
            ),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Material(
              color: Theme.of(context).colorScheme.secondary,
              shape: const CircleBorder(),
              child: InkWell(
                customBorder: const CircleBorder(),
                onTap: () => Navigator.of(context).pop(),
                child: const Padding(
                  padding: EdgeInsets.all(6.0),
                  child: Icon(Icons.close, size: 20, color: AppColorSchemes.white),
                ),
              ),
            ),
          ),
        ],
      ),
      insetPadding: EdgeInsets.symmetric(horizontal: 10),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [ 
          const Icon(
            Icons.warning_amber_outlined,
            color: AppColorSchemes.errorRed,
            size: 50,
          ),
          Text.rich(
            TextSpan(children: [
              TextSpan(
                text: "L'accés á votre espace est refusé pour le motif suivant",
                style: const TextStyle(
                  color: Colors.black,
                ),
              ),
              const TextSpan(
                text: "\n",
              ),
              TextSpan(
                text: message,
                style: const TextStyle(
                  color: AppColorSchemes.errorRed,
                ),
              ),
            ]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.spaceEvenly,
      actionsPadding: const EdgeInsets.all(15.0),
      actionsOverflowAlignment: OverflowBarAlignment.center,
      actionsOverflowButtonSpacing: 10,
      actions: [
        FilledButton(
          style: ButtonStyle(
            backgroundColor:
                WidgetStateProperty.all(AppColorSchemes.errorRed),
            minimumSize: WidgetStateProperty.all(const Size(170, 50)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.popUntil(context, ModalRoute.withName('/liste_etablissement'));
          },
          child: const Text('DÉCONNEXION'),
        ),
        FilledButton(
          style: ButtonStyle(
            backgroundColor:
                WidgetStateProperty.all(Theme.of(context).primaryColor),
            minimumSize: WidgetStateProperty.all(const Size(170, 50)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            debugPrint('Navigating to dashboard with student======>: ${student}');
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => BlocProvider(
                          create: (context) => sl<DashboardCubit>(),
                          child: Dashboard(enRegle: false),
                        ),
                        settings: RouteSettings(
                          arguments: {
                            'school': school,
                            'etudiant': student,
                          },
                        )
                      )
                        
                  );
          },
          child: const Text('CONTINUER'),
        ),
      ],
    );
  }
}