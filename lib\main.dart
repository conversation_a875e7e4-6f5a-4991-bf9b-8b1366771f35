import 'package:Kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart';
import 'package:Kairos/features/splash/presentation/bloc/splash_cubit.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart';
import 'package:Kairos/features/authentication/presentation/pages/activate_school/activate_school.page.dart';
import 'package:Kairos/features/authentication/presentation/pages/password_reset/password_reset.page.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/password_reset_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/financial_status_cubit.dart';
import 'package:Kairos/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:Kairos/features/profile/presentation/bloc/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import flutter_bloc
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil.page.dart';
import 'package:Kairos/features/dashboard/presentation/pages/dashboard/dashboard.page.dart';
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement.page.dart';
import 'package:Kairos/features/notifications/presentation/pages/notifications/notifications.page.dart';
import 'package:Kairos/features/notifications/presentation/bloc/notifications_cubit.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/profile/presentation/pages/profile/profile.page.dart';
import 'package:Kairos/features/splash/presentation/pages/splashscreen.dart';
import 'package:Kairos/features/schools/presentation/pages/dossier_selection/dossier_selection.page.dart';
import 'package:Kairos/features/student_records/presentation/bloc/student_records_cubit.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:Kairos/core/theme/app_theme.dart';
import "package:Kairos/core/di/injection_container.dart" as di;

void main() async{
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown
  ]);
  
  await di.init();
  await DeviceInfoService.init();
  runApp(const KairosMobileApp());
}


class KairosMobileApp extends StatelessWidget{
  const KairosMobileApp({super.key});

  @override
  Widget build(BuildContext context){
    // Wrap the MaterialApp with MultiBlocProvider for application-level Cubits
    return MultiBlocProvider(
      providers: [
        // Provide SchoolsCubit at the application root
        BlocProvider<SchoolsCubit>(
          create: (context) => di.sl<SchoolsCubit>(),
        ),
        // Provide AuthCubit at the application root
        BlocProvider<PhoneAuthenticationCubit>(
          create: (context) => di.sl<PhoneAuthenticationCubit>(),
        ),
        // Provide CodeActivationCubit at the application root
        BlocProvider<CodeActivationCubit>(
          create: (context) => di.sl<CodeActivationCubit>(),
        ),
        // Provide FinancialStatusCubit at the application root
        BlocProvider<FinancialStatusCubit>(
          create: (context) => di.sl<FinancialStatusCubit>(),
        ),
        // Provide DashboardCubit at the application root
        BlocProvider<DashboardCubit>(
          create: (context) => di.sl<DashboardCubit>(),
        ),
        // Provide ProfileCubit at the application root
        BlocProvider<ProfileCubit>(
          create: (context) => di.sl<ProfileCubit>(),
        ),
      ],
      child: MaterialApp(
          debugShowCheckedModeBanner: false,
          title: "Kairos Mobile",
          locale: Locale("fr", "FR"),
          
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale("fr", "FR"),
          ],
          theme: AppTheme.lightTheme,
          // Wrap Splashscreen with its specific BlocProvider
          home: BlocProvider<SplashCubit>(
            create: (context) => di.sl<SplashCubit>(),
            child: const Splashscreen(),
          ),
          builder: (context, child){
            if(child is Splashscreen || child is AccueilPage){
              return child!;
            } else {
              return SafeArea(child: child!);
            }
          },
          routes: {
            "/accueil": (context) => const AccueilPage(),
            "/liste_etablissement": (context) => const ListeEtablissement(),
            "/dashboard": (context) => Dashboard(),
            "/activate_school": (context) => const ActivateSchoolPage(),
            "/password_reset": (context) => BlocProvider(
              create: (context) => di.sl<PasswordResetCubit>(),
              child: const PasswordResetPage(),
            ),
            "/profile": (context) => ProfilePage(), // Pass null to etablissementUser to avoid errors
            "/notifications": (context) => BlocProvider(
              create: (context) => di.sl<NotificationsCubit>(),
              child: NotificationsPage(
                school: EtablissementUtilisateur(
                  codeEtab: 'SENSOFT',
                  codeUtilisateur: 'B100104',
                  libelleEtab: 'Test School',
                  logoEtablissement: '',
                  profil: 'ETU',
                ),
              ),
            ),
            "/dossier_selection": (context) => BlocProvider(
              create: (context) => di.sl<StudentRecordsCubit>(),
              child: const DossierSelectionPage(),
            ),
          }

        ),
    );
  }
}
