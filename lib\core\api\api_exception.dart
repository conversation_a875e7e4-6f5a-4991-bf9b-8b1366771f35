import 'api_error_response.dart';

/// Custom exception class for handling API errors
class ApiException implements Exception {
  final dynamic error;
  final int? statusCode;

  ApiException(this.error, [this.statusCode]);

  /// Factory constructor to create an ApiException from a JSON response
  factory ApiException.fromJson(Map<String, dynamic> json, [int? statusCode]) {
    // Try to determine the type of error based on the response structure
    if (json.containsKey('returnCode')) {
      return ApiException(ApiErrorResponse.fromJson(json), statusCode);
    } else if (json.containsKey('type') && json.containsKey('status')) {
      return ApiException(ValidationErrorResponse.fromJson(json), statusCode);
    }
    // Default case: unknown error format
    return ApiException({'message': 'Unknown error occurred'}, statusCode);
  }

  @override
  String toString() {
    if (error is ApiErrorResponse) {
      return (error as ApiErrorResponse).userMessage;
    } else if (error is ValidationErrorResponse) {
      return (error as ValidationErrorResponse).detail ?? '';
    }
    return error.toString();
  }

  /// Get user-friendly error message
  String getUserMessage() {
    if (error is ApiErrorResponse) {
      return (error as ApiErrorResponse).userMessage;
    } else if (error is ValidationErrorResponse) {
      final validationError = error as ValidationErrorResponse;
      if(validationError.status >= 500){
        return 'Erreur interne du serveur. Veuillez réessayer plus tard.';
      } else if (validationError.status == 401){
        return 'Erreur d\'authentification. Vous n\'êtes pas autorisé à accéder à cette ressource.';
      } else if (validationError.status == 400) {
        final fieldPath = validationError.parameters?['path'] ?? '';
        return 'Champ requis manquant: ${fieldPath.replaceAll('/', '')}';  
      } else {  
        return 'Erreur inconnue';  
      }   
    }
    return 'Une erreur inattendue s\'est produite';
  }

  /// Check if this is a specific type of error
  bool isUserAlreadyExistsError() {
    return error is ApiErrorResponse && 
           (error as ApiErrorResponse).returnCode == 4044;
  }

  /// Check if this is a validation error
  bool isValidationError() {
    return error is ValidationErrorResponse;
  }
}
