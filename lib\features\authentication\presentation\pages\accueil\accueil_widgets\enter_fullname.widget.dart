import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/auth_state.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';

class EnterFullnameWidget extends StatefulWidget{
  const EnterFullnameWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<EnterFullnameWidget> createState() => _EnterFullnameWidgetState();
}

class _EnterFullnameWidgetState extends State<EnterFullnameWidget>{

  final TextEditingController _fullnameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();
  bool _isLoading = false;

  @override
  void dispose(){
    super.dispose();
    _fullnameController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return BlocListener<CodeActivationCubit, AuthState>(
      listener: (context, state) {
        if (state is CodeActivationLoadingState) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is CodeActivationSuccessState) {
          setState(() {
            _isLoading = false;
          });
          // Navigate to the next page (AppActivatedWidget) on success
          widget.pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else if (state is CodeActivationErrorState) {
          setState(() {
            _isLoading = false;
          });
          // Navigate back to CodeActivationWidget on error
          // The error message will be displayed by the listener in CodeActivationWidget
          widget.pageController.previousPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text("ENTRER VOTRE NOM COMPLET", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
          SvgPicture.asset("assets/images/logo_kairos.svg"),
          SizedBox(height: 20,
                   width: 200,
                   child: Divider(color: Theme.of(context).primaryColor, thickness: 4),),
          Spacer(),
          Flexible(flex: 8, child: SvgPicture.asset("assets/images/illustration_profil.svg")),
          // Spacer(flex: 2),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Text("Veuillez saisir votre nom complet.", textAlign: TextAlign.center),
          ),
          Spacer(flex: 3),
          Form(
            key: _formKey,
            child: SizedBox(
              width: 300,
              child: TextFormField(
                controller: _fullnameController,
                decoration: InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: "Nom complet",
                  hintStyle: TextStyle(color: Colors.grey.shade400),
                  contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  ),
                keyboardType: TextInputType.name,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[^0-9]')),
                ],
                validator: (value){
                  if(value!.isEmpty){
                    return "Veuillez saisir votre nom complet";
                  } else {
                    return null;
                  }
                },
              ),

            ),
          ),
          Spacer(),
          FilledButton(
            style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                fixedSize: WidgetStateProperty.all(Size(300, 50))),
            onPressed: _isLoading ? null : () async {
              debugPrint('the user clicked on `Continue` button');
              if(_formKey.currentState!.validate()){
                setState(() {
                  _isLoading = true;
                });

                try {
                  // Save full name to SharedPreferences
                  await _authLocalDataSource.saveFullName(_fullnameController.text.trim());
                  debugPrint('Full name saved: ${_fullnameController.text.trim()}');

                  // Access phone number and activation code from PhoneAuthenticationCubit state
                  final phoneAuthState = context.read<PhoneAuthenticationCubit>().state;
                  if (phoneAuthState is AuthSmsSent) {
                    final phoneNumber = phoneAuthState.phoneNumber;
                    final activationCode = phoneAuthState.activationCode;
                    FocusScope.of(context).unfocus();
                    if (activationCode != null && activationCode.isNotEmpty) {
                      // Call verifyPin method in CodeActivationCubit
                      debugPrint('EnterFullnameWidget: Calling verifyPin with fullName: ${_fullnameController.text.trim()}, otp: $activationCode, phoneNumber: $phoneNumber');
                      context.read<CodeActivationCubit>().verifyPin(
                        _fullnameController.text.trim(),
                        activationCode,
                        phoneNumber,
                      );
                    } else {
                      // Handle case where activation code is missing (should not happen in this flow)
                      debugPrint('EnterFullnameWidget: Activation code is missing in PhoneAuthenticationCubit state.');
                      // Optionally show an error or navigate back
                      setState(() { _isLoading = false; });
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Code d\'activation manquant. Veuillez recommencer.'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      widget.pageController.jumpToPage(2); // Navigate back to Code Activation page
                    }
                  } else {
                    // Handle unexpected PhoneAuthenticationCubit state
                    debugPrint('EnterFullnameWidget: Unexpected PhoneAuthenticationCubit state: $phoneAuthState');
                    setState(() { _isLoading = false; });
                    ScaffoldMessenger.of(context).showSnackBar(
                    CustomSnackbar(
                        message: 'État inattendu. Veuillez recommencer.',
                        isError: true,
                      ).getSnackBar(),
                    );
                    widget.pageController.jumpToPage(1); // Navigate back to Phone Authentication page
                  }

                } catch (e) {
                  debugPrint('Error saving full name or verifying PIN: $e');
                  // Show error message to user
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      CustomSnackbar(
                        message: 'Erreur lors de la sauvegarde du nom complet ou de la vérification du code',
                        isError: true,
                      ).getSnackBar(),
                    );
                  }
                  setState(() { _isLoading = false; }); // Ensure loading is false on error
                }
              }
            },
            child: _isLoading
              ? const SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          Spacer(flex: 3),
        ],
      ),
    );
  }
}