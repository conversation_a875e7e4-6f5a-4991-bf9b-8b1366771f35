import 'package:Kairos/features/notifications/domain/entities/notification_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:Kairos/features/notifications/presentation/pages/notifications/notifications_widgets/notification_details_dialog.widget.dart';

class NotificationTile extends StatelessWidget {
  final NotificationEntity notification;
  final String formattedDate;
  const NotificationTile({
    super.key,
    required this.notification,
    required this.formattedDate,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Show the NotificationDetailsDialog when the tile is tapped
        debugPrint("details of the notificatoin ----> $notification");
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return NotificationDetailsDialog(notification: notification);
          },
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            left: BorderSide(
              color: Theme.of(context).primaryColor,
              width: 7,
            ),
          ),
          boxShadow: const [
            BoxShadow(
              color: Colors.blueGrey,
              blurRadius: 2,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: ListTile(
          // Add leading icon based on notification type
          title: Text(notification.title, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(HtmlParser.parseHTML(notification.mainContent).text, maxLines: 2, overflow: TextOverflow.ellipsis, style: const TextStyle(fontSize: 10,)),
              const SizedBox(height: 4),
              Text('De: ${notification.sender} | $formattedDate', style: const TextStyle(fontSize: 8, color: Colors.grey)),
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 4,
                    ),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(4),
                child: Image.asset('assets/images/ios-mail-application.png',
                  width: 36,
                  height: 36,
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
