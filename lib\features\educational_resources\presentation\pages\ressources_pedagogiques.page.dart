import 'package:flutter/material.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/educational_resources/presentation/pages/ressources_pedagogiques_widgets/resource_pedagogique_item.widget.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_cubit.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_state.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/core/di/injection_container.dart' as di;
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';

class RessourcesPedagogiquesPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const RessourcesPedagogiquesPage({
    super.key,
    required this.school,
    this.etudiant,
  }); 

  @override
  State<RessourcesPedagogiquesPage> createState() => _RessourcesPedagogiquesPageState();
}

class _RessourcesPedagogiquesPageState extends State<RessourcesPedagogiquesPage> with SingleTickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  String? _startDateFilter;
  String? _endDateFilter;
  final AuthLocalDataSource _authLocalDataSource = di.sl<AuthLocalDataSource>();

  late AnimationController _searchAnimationController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Fetch educational resources when the page loads
    _fetchRessourcesPedagogiques();
  }

  Future<void> _fetchRessourcesPedagogiques() async {
    final String? userPhoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (userPhoneNumber != null) {
      context.read<RessourcesPedagogiquesCubit>().fetchRessourcesPedagogiques(
        codeEtab: widget.school.codeEtab,
        telephone: userPhoneNumber,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur ?? '',
        codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur : null,
      );
    }
  }

  void onSearchTap() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear();
      } else {
        _searchAnimationController.forward();
      }
    });
  }

  void _onDateFilterChanged(Map<String, String> dateRange) async {
    final userPhoneNumber = await _authLocalDataSource.getPhoneNumber();
    if (userPhoneNumber != null && dateRange.containsKey('startDate') && dateRange.containsKey('endDate')) {
      setState(() {
        _startDateFilter = dateRange['startDate'];
        _endDateFilter = dateRange['endDate'];
      });

      context.read<RessourcesPedagogiquesCubit>().fetchRessourcesPedagogiquesByDateRange(
        codeEtab: widget.school.codeEtab,
        telephone: userPhoneNumber,
        codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur ?? '',
        codeUtilisateur: widget.etudiant != null ? widget.school.codeUtilisateur : null,
        dateDebut: dateRange['startDate'],
        dateFin: dateRange['endDate'],
      );
    }
  }

  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    _fetchRessourcesPedagogiques();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  List<ResourcePedagogiqueEntity> _getFilteredRessources(List<ResourcePedagogiqueEntity> allRessources) {
    final query = _searchController.text.toLowerCase();
    return allRessources.where((ressource) {
      // Text search filter
      bool matchesText = ressource.description.toLowerCase().contains(query) ||
             ressource.professeur.toLowerCase().contains(query) ||
             ressource.classe.toLowerCase().contains(query) ||
             ressource.dateAjout.toLowerCase().contains(query);

      return matchesText;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
              isSearchBarVisible: _isSearchBarVisible,
              onSearchTap: onSearchTap,
              pageSection: HeaderEnum.dossiers, // Using dossiers enum for consistency
              etablissementUtilisateur: widget.school,
              enfantDuTuteur: widget.etudiant,
              title: "RESSOURCES PÉDAGOGIQUES"),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0,
                  searchController: _searchController,
                  showYear: true,
                  onSearchChanged: (query) {
                    setState(() {
                      final filteredRessources = _getFilteredRessources((context.read<RessourcesPedagogiquesCubit>().state as RessourcesPedagogiquesLoaded).ressources);
                      debugPrint("all resources filtered -->$filteredRessources");
                    });
                  },
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher une ressource...",
                ),
              );
            },
          ),
          // Use BlocBuilder to react to RessourcesPedagogiquesCubit state changes
          BlocBuilder<RessourcesPedagogiquesCubit, RessourcesPedagogiquesState>(
            builder: (context, state) {
              if (state is RessourcesPedagogiquesLoading) {
                return SliverFillRemaining(
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                );
              } else if (state is RessourcesPedagogiquesLoaded) {
                final displayedRessources = _getFilteredRessources(state.ressources);
                return SliverFillRemaining(
                  child: displayedRessources.isEmpty
                      ? Center(child: EmptyMessage(message: "Aucune ressource pédagogique trouvée"))
                      : ListView.builder(
                          itemCount: displayedRessources.length,
                          itemBuilder: (context, index) {
                            final ressource = displayedRessources[index];
                            return ResourcePedagogiqueItem(
                              ressource: ressource,
                              school: widget.school,
                              etudiant: widget.etudiant,
                            );
                          },
                        ),
                );
              } else if (state is RessourcesPedagogiquesError) {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: state.message),
                  ),
                );
              }
              return SliverFillRemaining(
                child: Center(
                  child: EmptyMessage(message: "Aucune ressource pédagogique disponible"),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
