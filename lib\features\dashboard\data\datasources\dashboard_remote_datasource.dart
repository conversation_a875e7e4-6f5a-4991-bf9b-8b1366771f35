import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../../../core/api/api_client.dart';
import '../models/dashboard_model.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/api/api_exception.dart';

/// Abstract remote data source for dashboard operations
abstract class DashboardRemoteDataSource {
  /// Load dashboard data from remote API
  Future<DashboardModel> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of DashboardRemoteDataSource
class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiClient apiClient;

  DashboardRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<DashboardModel> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Build query parameters based on whether codeUtilisateur is provided
      final queryParameters = <String, dynamic>{
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };

      // Add codeUtilisateur if provided (for parent/tutor workflow)
      if (codeUtilisateur != null) {
        queryParameters['codeUtilisateur'] = codeUtilisateur;
      }

      // Make API call with query parameters
      final response = await apiClient.getWithToken(
        '/dashboard',
        queryParameters: queryParameters,
      );

      // Parse response and return model
      return DashboardModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('DashboardRemoteDataSourceImpl: DioException: ${e}');
      if (e.response != null) {
        // Handle API error response
        debugPrint('DashboardRemoteDataSourceImpl: DioException: $e');
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } on ServerException catch (e) {
      debugPrint('DashboardRemoteDataSourceImpl: ServerException --->');
      debugPrint('DashboardRemoteDataSourceImpl: ServerException: ${e.message}');
      rethrow;
      
    } catch (e) {
      // For any other unexpected errors, throw a generic exception
      throw Exception('Failed to load dashboard data: $e');
    }
  }
}
