import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:kairos/features/authentication/domain/usecases/activate_school_usecase.dart';
import 'package:kairos/features/authentication/presentation/bloc/state/activate_school_state.dart';
import 'package:kairos/features/schools/data/datasources/schools_local_datasource.dart';
import 'package:kairos/features/schools/data/models/user_profile_model.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';



// Define Cubit
class ActivateSchoolCubit extends Cubit<ActivateSchoolState> {
  final ActivateSchoolUseCase _activateSchoolUseCase;
  final SchoolsLocalDataSource _schoolsLocalDataSource;

  ActivateSchoolCubit(
    this._activateSchoolUseCase,
    this._schoolsLocalDataSource,
  ):super(const ActivateSchoolInitial()); // Initialize super with ActivateSchoolInitial state

  /// Validate authentication and activate school with the provided credentials.
  /// This method calls the getUserSchools endpoint to validate authentication.
  Future<void> activateSchool(ActivationRequestEntity requestEntity) async {
    emit(const ActivateSchoolLoading()); // Indicate loading state

    try {

      // Call getUserProfileModels to get the list of UserProfileModel objects
      final failureOrUserSchools = await _activateSchoolUseCase(requestEntity);

      failureOrUserSchools.fold(
        (failure) {
          // Handle failure and emit error state
          debugPrint('Failed to get user schools: ${_mapFailureToMessage(failure)}');
          emit(ActivateSchoolError(_mapFailureToMessage(failure)));
        },
        (userProfiles) async {
          // Find matching user profile based on codeUtilisateur and codeEtab
          final matchingProfile = _findMatchingUserProfile(
            userProfiles,
            requestEntity.username,
            requestEntity.codeEtab
          );

          if (matchingProfile == null) {
            emit(const ActivateSchoolError('Aucun profil correspondant trouvé pour ces identifiants.'));
            return;
          }

          // Check authentication status
          if (matchingProfile.isAuthenticated != true) {
            emit(const ActivateSchoolError('Vous n\'êtes pas autorisé(e) à utiliser cet environnement.'));
            return;
          }

          // Store data in SharedPreferences
          await _storeUserData(matchingProfile);

          // Emit success state
          emit(ActivateSchoolSuccess(userProfiles.map((profile) => profile.toEntity()).toList()));
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during school activation: $e');
      emit(ActivateSchoolError('Une erreur inattendue s\'est produite: $e'));
    }
  }


  /// Find matching user profile based on codeUtilisateur and codeEtab
  UserProfileModel? _findMatchingUserProfile(
    List<UserProfileModel> userProfiles,
    String codeUtilisateur,
    String codeEtab,
  ) {
    try {
      return userProfiles.firstWhere(
        (profile) =>
          profile.codeUtilisateur == codeUtilisateur &&
          profile.etablissement.codeEtab == codeEtab,
      );
    } catch (e) {
      debugPrint('No matching profile found: $e');
      return null;
    }
  }

  /// Store user data in SharedPreferences
  Future<void> _storeUserData(
    UserProfileModel matchingProfile,
  ) async {
    try {
      // Store the complete matching UserProfileModel
      await _schoolsLocalDataSource.storeUserProfile(matchingProfile);

      // Store the nested utilisateur object
      await _schoolsLocalDataSource.storeUser(matchingProfile.utilisateur);

      // Store the nested etablissement object
      await _schoolsLocalDataSource.storeSchool(matchingProfile.etablissement);

      debugPrint('User data stored successfully');
    } catch (e) {
      debugPrint('Error storing user data: $e');
      throw Exception('Erreur lors de la sauvegarde des données utilisateur');
    }
  }

  String _mapFailureToMessage(Failure failure) {
    // Implement logic to map different failure types to user-friendly messages
    debugPrint('Failure type: ${failure.runtimeType}');
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return failure.message;
    } else if (failure is AuthenticationFailure) {
      return failure.message;
    } else {
      return failure.message;
    }
  }
}
